import { useState, useEffect } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  LineChart, 
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  ScatterChart,
  Scatter
} from "recharts";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/common/DataTable";
import { 
  ArrowDownToLine, 
  Calendar, 
  Filter, 
  LineChart as LineChartIcon,
  Printer, 
  Settings
} from "lucide-react";
import { 
  collection, 
  getDocs,
  query,
  where,
  Timestamp,
  orderBy,
  limit
} from 'firebase/firestore';
import { firebaseFirestore } from "@/lib/firebase"; // Updated import
import { Product, Sale } from "@/types/firebase";
import { format, subDays, subMonths, parseISO, startOfMonth, endOfMonth, isAfter, isBefore, isWithinInterval } from "date-fns";
import { toast } from "sonner";

// Colors for pie chart
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

export default function Analytics() {
  const [selectedTab, setSelectedTab] = useState("overview");
  const [sales, setSales] = useState<Sale[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateFilter, setDateFilter] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  
  // Fetch data from Firestore
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch sales data
        const salesRef = collection(firebaseFirestore, "sales");
        const salesQuery = query(
          salesRef,
          orderBy("timestamp", "desc"),
          limit(100) // Limit to last 100 sales for performance
        );
        const salesSnapshot = await getDocs(salesQuery);
        const fetchedSales = salesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Sale[];
        
        // Fetch products data
        const productsRef = collection(firebaseFirestore, "products");
        const productsSnapshot = await getDocs(productsRef);
        const fetchedProducts = productsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Product[];
        
        setSales(fetchedSales);
        setProducts(fetchedProducts);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load analytics data. Please try again.");
        toast.error("Failed to load analytics data");
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  // Function to parse dates from sales data
  const getSaleDate = (sale: Sale) => {
    if (sale.timestamp && typeof sale.timestamp.toDate === 'function') {
      return sale.timestamp.toDate();
    } else if (sale.date) {
      try {
        const date = parseISO(sale.date);
        if (!isNaN(date.getTime())) return date;
      } catch (error) {
        console.error("Invalid date format:", sale.date);
      }
    }
    return null; // Invalid or missing date
  };
  
  // Get daily store traffic data (approximated from sales)
  const getDailyTrafficData = () => {
    if (sales.length === 0) return [];
    
    // Initialize with hourly buckets
    const hours = ['8 AM', '9 AM', '10 AM', '11 AM', '12 PM', '1 PM', '2 PM', 
                  '3 PM', '4 PM', '5 PM', '6 PM', '7 PM', '8 PM', '9 PM'];
    const hourMap = new Map<string, number>();
    
    // Initialize the map with zeros
    hours.forEach(hour => {
      hourMap.set(hour, 0);
    });
    
    // Count sales by hour
    sales.forEach(sale => {
      const date = getSaleDate(sale);
      if (date) {
        const hour = format(date, 'h a');
        if (hourMap.has(hour)) {
          const currentValue = hourMap.get(hour);
          if (currentValue !== undefined) {
            hourMap.set(hour, currentValue + 1);
          }
        }
      }
    });
    
    // Convert to array for the chart
    return hours.map(hour => ({
      time: hour,
      visitors: (hourMap.get(hour) || 0) * 3 // Multiplying by 3 to estimate total visitors from sales
    }));
  };
  
  // Get revenue and profit trend data for last 30 days
  const getRevenueTrendData = () => {
    if (sales.length === 0) return [];

    const dailyRevenue = new Map<string, number>();
    const dailyProfit = new Map<string, number>();

    // Initialize days
    for (let i = 29; i >= 0; i--) {
      const day = format(subDays(new Date(), i), 'd');
      dailyRevenue.set(day, 0);
      dailyProfit.set(day, 0);
    }

    // Sum revenue and profit by day
    sales.forEach(sale => {
      const date = getSaleDate(sale);
      const thirtyDaysAgo = subDays(new Date(), 30);
      const now = new Date();
      if (date && isWithinInterval(date, { start: thirtyDaysAgo, end: now })) {
        const day = format(date, 'd');
        if (dailyRevenue.has(day)) {
          const currentRevenue = dailyRevenue.get(day);
          const currentProfit = dailyProfit.get(day);

          if (currentRevenue !== undefined) {
            dailyRevenue.set(day, currentRevenue + sale.amount);
          }

          if (currentProfit !== undefined) {
            dailyProfit.set(day, currentProfit + calculateProfitForSale(sale));
          }
        }
      }
    });

    // Convert to array for the chart
    return Array.from(dailyRevenue.entries()).map(([day, revenue]) => ({
      day,
      revenue,
      profit: dailyProfit.get(day) || 0
    }));
  };
  
  // Get category performance data with accurate profit calculations
  const getCategoryPerformanceData = () => {
    if (sales.length === 0 || products.length === 0) return [];

    const categorySales = new Map<string, number>();
    const categoryProfit = new Map<string, number>();
    const categoryUnits = new Map<string, number>();

    // Initialize categories
    products.forEach(product => {
      const category = product.category || "Uncategorized";
      if (!categorySales.has(category)) {
        categorySales.set(category, 0);
        categoryProfit.set(category, 0);
        categoryUnits.set(category, 0);
      }
    });

    // Calculate sales and profit by category
    sales.forEach(sale => {
      sale.products?.forEach(item => {
        const product = products.find(p => p.id === item.productId);
        if (product) {
          const category = product.category || "Uncategorized";
          const saleAmount = item.quantity * item.price;

          // Update sales
          const currentSales = categorySales.get(category);
          if (currentSales !== undefined) {
            categorySales.set(category, currentSales + saleAmount);
          }

          // Update units sold
          const currentUnits = categoryUnits.get(category);
          if (currentUnits !== undefined) {
            categoryUnits.set(category, currentUnits + item.quantity);
          }

          // Calculate accurate profit
          const costPrice = product.buyingPrice || (product.sellingPrice || product.price) * 0.7;
          const profit = (item.price - costPrice) * item.quantity;

          const currentProfit = categoryProfit.get(category);
          if (currentProfit !== undefined) {
            categoryProfit.set(category, currentProfit + profit);
          }
        }
      });
    });

    // Convert to array for the chart
    return Array.from(categorySales.keys()).map(name => ({
      name,
      sales: Math.round(categorySales.get(name) || 0),
      profit: Math.round(categoryProfit.get(name) || 0),
      units: categoryUnits.get(name) || 0,
      profitMargin: categorySales.get(name) ?
        Math.round(((categoryProfit.get(name) || 0) / (categorySales.get(name) || 1)) * 100) : 0
    })).sort((a, b) => b.sales - a.sales).slice(0, 5); // Top 5 categories
  };
  
  // Get customer segment data
  const getCustomerSegmentData = () => {
    if (sales.length === 0) return [];
    
    // Group customers by frequency
    const customerFrequency = new Map<string, number>();
    
    sales.forEach(sale => {
      if (sale.customer) {
        const currentFreq = customerFrequency.get(sale.customer) || 0;
        customerFrequency.set(sale.customer, currentFreq + 1);
      }
    });
    
    // Count customers by segment
    let newCount = 0;        // 1 purchase
    let returningCount = 0;  // 2-5 purchases
    let loyalCount = 0;      // 6+ purchases
    
    customerFrequency.forEach((count) => {
      if (count === 1) newCount++;
      else if (count >= 2 && count <= 5) returningCount++;
      else loyalCount++;
    });
    
    // Calculate percentages
    const total = newCount + returningCount + loyalCount;
    if (total === 0) return [
      { name: "No Data", value: 100 }
    ];
    
    return [
      { name: "New", value: Math.round((newCount / total) * 100) },
      { name: "Returning", value: Math.round((returningCount / total) * 100) },
      { name: "Loyal", value: Math.round((loyalCount / total) * 100) }
    ];
  };
  
  // Get key products performance
  const getKeyProductsData = () => {
    if (sales.length === 0 || products.length === 0) return [];

    const now = new Date();
    const thirtyDaysAgo = subDays(now, 30);
    const sixtyDaysAgo = subDays(now, 60);

    const currentPeriodSales = new Map<string, number>();
    const currentPeriodRevenue = new Map<string, number>();
    const previousPeriodSales = new Map<string, number>();
    const previousPeriodRevenue = new Map<string, number>();

    // Calculate sales and revenue by product for current and previous periods
    sales.forEach(sale => {
      const saleDate = getSaleDate(sale);
      if (!saleDate) return;

      const isCurrentPeriod = saleDate >= thirtyDaysAgo;
      const isPreviousPeriod = saleDate >= sixtyDaysAgo && saleDate < thirtyDaysAgo;

      sale.products?.forEach(item => {
        if (isCurrentPeriod) {
          const currentSales = currentPeriodSales.get(item.productId) || 0;
          currentPeriodSales.set(item.productId, currentSales + item.quantity);

          const currentRevenue = currentPeriodRevenue.get(item.productId) || 0;
          currentPeriodRevenue.set(item.productId, currentRevenue + (item.quantity * item.price));
        }

        if (isPreviousPeriod) {
          const previousSales = previousPeriodSales.get(item.productId) || 0;
          previousPeriodSales.set(item.productId, previousSales + item.quantity);

          const previousRevenue = previousPeriodRevenue.get(item.productId) || 0;
          previousPeriodRevenue.set(item.productId, previousRevenue + (item.quantity * item.price));
        }
      });
    });

    // Create product performance data
    const productPerformance = products.map(product => {
      const currentSales = currentPeriodSales.get(product.id) || 0;
      const currentRevenue = currentPeriodRevenue.get(product.id) || 0;
      const previousSales = previousPeriodSales.get(product.id) || 0;

      // Calculate real growth based on previous period
      let growth = 0;
      if (previousSales > 0) {
        growth = Math.round(((currentSales - previousSales) / previousSales) * 100);
      } else if (currentSales > 0) {
        growth = 100; // New product with sales
      }

      return {
        id: product.id,
        name: product.name,
        sales: currentSales,
        revenue: currentRevenue,
        growth
      };
    });

    // Return top 8 products by revenue
    return productPerformance
      .filter(product => product.revenue > 0) // Only show products with sales
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 8);
  };
  
  // Enhanced profit calculation functions
  const calculateProfitForSale = (sale: Sale) => {
    let totalProfit = 0;

    sale.products?.forEach(item => {
      const product = products.find(p => p.id === item.productId);
      if (product) {
        // Use buyingPrice if available, otherwise estimate as 70% of selling price
        const costPrice = product.buyingPrice || (product.sellingPrice || product.price) * 0.7;
        const sellingPrice = item.price;
        const profit = (sellingPrice - costPrice) * item.quantity;
        totalProfit += profit;
      }
    });

    return totalProfit;
  };

  // Get profit for different time periods
  const getProfitByPeriod = (periodType: 'daily' | 'weekly' | 'monthly' | 'annual') => {
    const now = new Date();
    let startDate: Date;

    switch (periodType) {
      case 'daily':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'weekly':
        startDate = subDays(now, 7);
        break;
      case 'monthly':
        startDate = startOfMonth(now);
        break;
      case 'annual':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = startOfMonth(now);
    }

    return sales
      .filter(sale => {
        const saleDate = getSaleDate(sale);
        return saleDate && isWithinInterval(saleDate, { start: startDate, end: now });
      })
      .reduce((total, sale) => total + calculateProfitForSale(sale), 0);
  };

  // Calculate metrics
  const getTotalRevenue = () => {
    return sales.reduce((total, sale) => total + sale.amount, 0).toFixed(2);
  };

  const getTotalProfit = () => {
    return sales.reduce((total, sale) => total + calculateProfitForSale(sale), 0).toFixed(2);
  };

  const getDailyProfit = () => {
    return getProfitByPeriod('daily').toFixed(2);
  };

  const getWeeklyProfit = () => {
    return getProfitByPeriod('weekly').toFixed(2);
  };

  const getMonthlyProfit = () => {
    return getProfitByPeriod('monthly').toFixed(2);
  };

  const getAnnualProfit = () => {
    return getProfitByPeriod('annual').toFixed(2);
  };

  const getSalesCount = () => {
    return sales.length;
  };

  const getAverageOrderValue = () => {
    if (sales.length === 0) return "0.00";
    return (sales.reduce((total, sale) => total + sale.amount, 0) / sales.length).toFixed(2);
  };

  const getCustomerCount = () => {
    const uniqueCustomers = new Set();
    sales.forEach(sale => {
      if (sale.customer) uniqueCustomers.add(sale.customer);
    });
    return uniqueCustomers.size;
  };

  // Get customer acquisition data from real sales
  const getCustomerAcquisitionData = () => {
    if (sales.length === 0) return [];

    // Group sales by month and track customers
    const monthlyCustomers = new Map<string, Set<string>>();
    const monthlyReturning = new Map<string, Set<string>>();
    const allTimeCustomers = new Set<string>();

    // Process sales to track customer acquisition
    sales.forEach(sale => {
      const saleDate = getSaleDate(sale);
      if (saleDate && sale.customer) {
        const monthKey = format(saleDate, 'MMM');

        // Initialize month if not exists
        if (!monthlyCustomers.has(monthKey)) {
          monthlyCustomers.set(monthKey, new Set());
          monthlyReturning.set(monthKey, new Set());
        }

        // Add customer to this month
        monthlyCustomers.get(monthKey)?.add(sale.customer);

        // Check if this is a returning customer
        if (allTimeCustomers.has(sale.customer)) {
          monthlyReturning.get(monthKey)?.add(sale.customer);
        } else {
          allTimeCustomers.add(sale.customer);
        }
      }
    });

    // Generate data for the last 12 months
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    return months.map(month => {
      const totalCustomers = monthlyCustomers.get(month)?.size || 0;
      const returningCustomers = monthlyReturning.get(month)?.size || 0;
      const newCustomers = totalCustomers - returningCustomers;

      return {
        month,
        newCustomers: Math.max(0, newCustomers),
        returningCustomers
      };
    });
  };

  // Handler functions for filter buttons
  const handleDateFilterChange = (filter: '7d' | '30d' | '90d' | 'all') => {
    setDateFilter(filter);
    toast.success(`Filter changed to ${filter === '7d' ? '7 days' : filter === '30d' ? '30 days' : filter === '90d' ? '90 days' : 'all time'}`);
  };

  const handleExportData = () => {
    try {
      const dataToExport = {
        products: keyProducts,
        revenue: getTotalRevenue(),
        profit: getTotalProfit(),
        salesCount: getSalesCount(),
        customerCount: getCustomerCount(),
        exportDate: new Date().toISOString()
      };

      const dataStr = JSON.stringify(dataToExport, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `analytics-export-${format(new Date(), 'yyyy-MM-dd')}.json`;
      link.click();
      URL.revokeObjectURL(url);

      toast.success('Analytics data exported successfully');
    } catch (error) {
      toast.error('Failed to export data');
    }
  };

  const handlePrintReport = () => {
    window.print();
    toast.success('Print dialog opened');
  };

  const handleCustomizeView = () => {
    toast.info('Customize view feature coming soon');
  };

  // Process data for UI
  const dailyTrafficData = getDailyTrafficData();
  const customerSegments = getCustomerSegmentData();
  const categoryPerformance = getCategoryPerformanceData();
  const keyProducts = getKeyProductsData();
  const revenueTrend = getRevenueTrendData();
  const customerAcquisitionData = getCustomerAcquisitionData();

  if (error) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <div className="text-center space-y-4">
          <p className="text-red-500">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 animate-fade-in">
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Store Analytics</h1>
        <p className="text-muted-foreground text-sm sm:text-base">
          Detailed insights into your stationery shop's sales performance and customer trends.
        </p>
      </div>
      
      <Tabs defaultValue="overview" value={selectedTab} onValueChange={setSelectedTab}>
        <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <TabsList className="w-full sm:w-auto">
            <TabsTrigger value="overview" className="text-xs sm:text-sm">Overview</TabsTrigger>
            <TabsTrigger value="sales" className="text-xs sm:text-sm">Sales</TabsTrigger>
            <TabsTrigger value="customers" className="text-xs sm:text-sm">Customers</TabsTrigger>
            <TabsTrigger value="products" className="text-xs sm:text-sm">Products</TabsTrigger>
          </TabsList>

          <div className="flex items-center space-x-1 sm:space-x-2 overflow-x-auto">
            <Button
              variant={dateFilter === '30d' ? 'default' : 'outline'}
              size="sm"
              className="text-xs sm:text-sm whitespace-nowrap"
              onClick={() => handleDateFilterChange('30d')}
            >
              <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Last 30 Days</span>
              <span className="sm:hidden">30d</span>
            </Button>
            <Button
              variant={dateFilter === '7d' ? 'default' : 'outline'}
              size="sm"
              className="text-xs sm:text-sm whitespace-nowrap"
              onClick={() => handleDateFilterChange('7d')}
            >
              <span className="hidden sm:inline">7 Days</span>
              <span className="sm:hidden">7d</span>
            </Button>
            <Button
              variant={dateFilter === '90d' ? 'default' : 'outline'}
              size="sm"
              className="text-xs sm:text-sm whitespace-nowrap"
              onClick={() => handleDateFilterChange('90d')}
            >
              <span className="hidden sm:inline">90 Days</span>
              <span className="sm:hidden">90d</span>
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 sm:h-10 sm:w-10"
              onClick={handleExportData}
              title="Export Data"
            >
              <ArrowDownToLine className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 sm:h-10 sm:w-10"
              onClick={handlePrintReport}
              title="Print Report"
            >
              <Printer className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
          </div>
        </div>
        
        <TabsContent value="overview" className="space-y-4 mt-4">
          {/* Main Metrics Grid */}
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Tsh {loading ? "..." : getTotalRevenue()}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Profit</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Tsh {loading ? "..." : getTotalProfit()}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Sales Count</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{loading ? "..." : getSalesCount()}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Customer Count</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{loading ? "..." : getCustomerCount()}</div>
              </CardContent>
            </Card>
          </div>

          {/* Profit Breakdown Cards */}
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Daily Profit</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-green-600">Tsh {loading ? "..." : getDailyProfit()}</div>
                <p className="text-xs text-muted-foreground">Today's earnings</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Weekly Profit</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-green-600">Tsh {loading ? "..." : getWeeklyProfit()}</div>
                <p className="text-xs text-muted-foreground">Last 7 days</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Monthly Profit</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-green-600">Tsh {loading ? "..." : getMonthlyProfit()}</div>
                <p className="text-xs text-muted-foreground">This month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Annual Profit</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-green-600">Tsh {loading ? "..." : getAnnualProfit()}</div>
                <p className="text-xs text-muted-foreground">This year</p>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Revenue & Profit Trend</CardTitle>
                  <CardDescription>Daily revenue and profit for the last month</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <LineChartIcon className="h-4 w-4 mr-2" />
                  Details
                </Button>
              </div>
            </CardHeader>
            <CardContent className="h-64 sm:h-80">
              {loading ? (
                <div className="flex h-full items-center justify-center">
                  <p>Loading data...</p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={revenueTrend}
                    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis
                      dataKey="day"
                      tickFormatter={(value) => (parseInt(value) % 5 === 0 || value === "1" || value === "30") ? value : ""}
                    />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [`Tsh${value}`, name === "revenue" ? "Revenue" : "Profit"]}
                      contentStyle={{
                        backgroundColor: "white",
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                        border: "none",
                      }}
                    />
                    <Legend />
                    <Area type="monotone" dataKey="revenue" stackId="1" stroke="hsl(var(--primary))" fill="hsl(var(--primary)/.2)" name="Revenue" />
                    <Area type="monotone" dataKey="profit" stackId="2" stroke="#22c55e" fill="#22c55e20" name="Profit" />
                  </AreaChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Daily Store Traffic</CardTitle>
                <CardDescription>
                  Hourly visitor count for today
                </CardDescription>
              </CardHeader>
              <CardContent className="h-64 sm:h-80">
                {loading ? (
                  <div className="flex h-full items-center justify-center">
                    <p>Loading data...</p>
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={dailyTrafficData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis dataKey="time" />
                      <YAxis />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: "white",
                          borderRadius: "8px",
                          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                          border: "none",
                        }}
                      />
                      <Bar dataKey="visitors" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Customer Segments</CardTitle>
                <CardDescription>
                  Distribution of customer types
                </CardDescription>
              </CardHeader>
              <CardContent className="h-64 sm:h-80">
                {loading ? (
                  <div className="flex h-full items-center justify-center">
                    <p>Loading data...</p>
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={customerSegments}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {customerSegments.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}%`, "Percentage"]} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="sales" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Sales by Category</CardTitle>
              <CardDescription>
                Performance comparison across product categories
              </CardDescription>
            </CardHeader>
            <CardContent className="h-64 sm:h-80 lg:h-96">
              {loading ? (
                <div className="flex h-full items-center justify-center">
                  <p>Loading data...</p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={categoryPerformance}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => [`Tsh${value}`, "Amount"]}
                      contentStyle={{
                        backgroundColor: "white",
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                        border: "none",
                      }}
                    />
                    <Legend />
                    <Bar dataKey="sales" name="Sales" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="profit" name="Profit" fill="#22c55e" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="customers" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Acquisition</CardTitle>
              <CardDescription>
                New customers over time
              </CardDescription>
            </CardHeader>
            <CardContent className="h-64 sm:h-80 lg:h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={customerAcquisitionData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "white",
                      borderRadius: "8px",
                      boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                      border: "none",
                    }}
                  />
                  <Legend />
                  <Line type="monotone" dataKey="newCustomers" name="New Customers" stroke="hsl(var(--primary))" strokeWidth={2} />
                  <Line type="monotone" dataKey="returningCustomers" name="Returning Customers" stroke="#22c55e" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="products" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Key Products Performance</CardTitle>
                  <CardDescription>
                    Sales and revenue analysis of top products
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm" onClick={handleCustomizeView}>
                  <Settings className="h-4 w-4 mr-2" />
                  Customize
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex h-40 items-center justify-center">
                  <p>Loading data...</p>
                </div>
              ) : (
                <DataTable
                  data={keyProducts}
                  columns={[
                    {
                      id: "name",
                      header: "Product",
                      cell: (row) => <div className="font-medium">{row.name}</div>,
                    },
                    {
                      id: "sales",
                      header: "Units Sold",
                      cell: (row) => <div>{row.sales}</div>,
                    },
                    {
                      id: "revenue",
                      header: "Revenue",
                      cell: (row) => <div className="font-medium">${row.revenue.toLocaleString()}</div>,
                    },
                    {
                      id: "growth",
                      header: "Growth",
                      cell: (row) => (
                        <div className={`flex items-center ${row.growth >= 0 ? "text-green-500" : "text-red-500"}`}>
                          {row.growth >= 0 ? "↑" : "↓"} {Math.abs(row.growth)}%
                        </div>
                      ),
                    },
                  ]}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}