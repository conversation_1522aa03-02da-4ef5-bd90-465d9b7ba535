# Expense Management System Test Plan

## Overview
This document outlines the testing plan for the newly implemented expense management system in Z&D stationery.

## Features Implemented

### 1. Application Branding ✅
- Updated application name from "PaperCraft" to "Z&D stationery"
- Updated in:
  - `index.html` - Page title
  - `src/components/common/Sidebar.tsx` - Sidebar branding and footer
  - `src/pages/Login.tsx` - Login page header
  - `src/pages/Settings.tsx` - Business name default value

### 2. Expense Data Type & Firebase Integration ✅
- Added `Expense` interface in `src/types/firebase.ts` with comprehensive fields:
  - Basic info: title, description, amount, category, date
  - Payment details: paymentMethod, vendor, receiptNumber
  - Workflow: status (pending/approved/rejected), createdBy
  - Advanced: isRecurring, recurringFrequency, tags
  - Timestamps: createdAt, updatedAt

- Added `expenseService` in `src/lib/firebase.ts` with full CRUD operations:
  - `getAll()` - Get all expenses
  - `getByUser(userId)` - Get expenses by user
  - `getByStatus(status)` - Get expenses by status
  - `add(expenseData)` - Add new expense
  - `update(id, expenseData)` - Update expense
  - `delete(id)` - Delete expense
  - `getById(id)` - Get expense by ID
  - `calculateTotal(filters)` - Calculate total with filters
  - `getCategorySummary()` - Get category breakdown

- Added demo expense data with 5 sample expenses

### 3. Expenses Page Component ✅
- Created comprehensive `src/pages/Expenses.tsx` with:
  - Three tabs: Overview, All Expenses, Analytics
  - Real-time metrics and calculations
  - Interactive charts (Pie chart for categories)
  - Data table with sorting and actions
  - Expense detail view dialog
  - Admin-only actions (add, edit, delete)

- Created `src/components/expenses/AddExpenseForm.tsx` with:
  - Complete form with all expense fields
  - Validation for required fields and amount
  - Support for editing existing expenses
  - Recurring expense options
  - Tag management
  - Status management

### 4. Navigation & Routing ✅
- Added expenses route to `src/App.tsx`
- Added "Expenses" navigation item to admin sidebar
- Used Receipt icon for expenses navigation
- Positioned between Staff and Analytics in navigation

## Test Cases

### Manual Testing Checklist

#### 1. Application Branding
- [ ] Verify page title shows "Z&D stationery - Stationery Shop Management"
- [ ] Verify sidebar shows "Z&D stationery" when expanded
- [ ] Verify sidebar shows "Z&D" when collapsed
- [ ] Verify login page shows "Z&D stationery" header
- [ ] Verify settings page shows "Z&D stationery" as default business name
- [ ] Verify footer shows "© 2025 Z&D stationery"

#### 2. Navigation & Access
- [ ] Login as admin user (<EMAIL> / password)
- [ ] Verify "Expenses" appears in sidebar navigation
- [ ] Click on Expenses navigation item
- [ ] Verify expenses page loads successfully
- [ ] Verify URL shows `/expenses`

#### 3. Expenses Overview Tab
- [ ] Verify metrics cards display:
  - This Month total
  - Pending Approval amount and count
  - Approved Total amount and count
  - Categories count
- [ ] Verify pie chart shows expense categories
- [ ] Verify recent expenses list shows latest 5 expenses
- [ ] Verify each expense shows title, category, amount, and status

#### 4. Add New Expense
- [ ] Click "Add Expense" button
- [ ] Verify form dialog opens
- [ ] Fill in required fields (Title, Amount, Category, Date)
- [ ] Test form validation:
  - [ ] Try submitting with empty title
  - [ ] Try submitting with invalid amount
  - [ ] Try submitting without category
- [ ] Fill in optional fields (Description, Vendor, Receipt Number, Tags)
- [ ] Test recurring expense checkbox
- [ ] Submit valid expense
- [ ] Verify success toast appears
- [ ] Verify expense appears in the list

#### 5. View Expense Details
- [ ] Click eye icon on any expense
- [ ] Verify detail dialog opens
- [ ] Verify all expense information is displayed correctly
- [ ] Close dialog

#### 6. Edit Expense
- [ ] Click edit icon on any expense
- [ ] Verify form opens with existing data
- [ ] Modify some fields
- [ ] Submit changes
- [ ] Verify success toast appears
- [ ] Verify changes are reflected in the list

#### 7. Delete Expense
- [ ] Click delete icon on any expense
- [ ] Verify confirmation dialog appears
- [ ] Confirm deletion
- [ ] Verify success toast appears
- [ ] Verify expense is removed from list

#### 8. All Expenses Tab
- [ ] Click "All Expenses" tab
- [ ] Verify data table shows all expenses
- [ ] Test table sorting by clicking column headers
- [ ] Verify pagination works if many expenses
- [ ] Verify export button is present

#### 9. Analytics Tab
- [ ] Click "Analytics" tab
- [ ] Verify monthly trend comparison
- [ ] Verify category breakdown with amounts and averages
- [ ] Verify trend indicators (up/down arrows)

#### 10. Firebase Integration
- [ ] Add a new expense
- [ ] Refresh the page
- [ ] Verify expense persists (saved to Firebase)
- [ ] Check browser network tab for Firebase calls
- [ ] Verify real-time updates work

#### 11. Responsive Design
- [ ] Test on mobile viewport
- [ ] Verify forms are usable on small screens
- [ ] Verify tables are responsive
- [ ] Verify navigation works on mobile

#### 12. Permission Testing
- [ ] Login as shopkeeper user (<EMAIL> / password)
- [ ] Verify "Expenses" does NOT appear in navigation
- [ ] Try accessing `/expenses` directly
- [ ] Verify appropriate access control

## Expected Results

### Demo Data
The system should show 5 demo expenses:
1. Office Rent - $1,200.00 (Rent & Utilities, Approved)
2. Electricity Bill - $150.75 (Rent & Utilities, Approved)
3. Office Supplies Purchase - $350.25 (Inventory, Approved)
4. Marketing Campaign - $200.00 (Marketing, Approved)
5. Equipment Maintenance - $125.50 (Maintenance, Pending)

### Calculations
- Total approved expenses: $2,026.00
- Total pending expenses: $125.50
- Categories: 5 (Rent & Utilities, Inventory, Marketing, Maintenance)

## Known Limitations
- Export functionality is placeholder (button present but not functional)
- No file upload for receipts
- No email notifications for expense approvals
- No budget limits or warnings

## Success Criteria
✅ All branding updated to Z&D stationery
✅ Expense management system fully functional
✅ Firebase integration working with real data
✅ Admin can perform all CRUD operations
✅ Calculations and analytics working correctly
✅ Responsive design working on all screen sizes
✅ Proper access control for admin vs shopkeeper roles
