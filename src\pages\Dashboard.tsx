import { 
  BarChart4, 
  DollarSign, 
  Package, 
  ShoppingCart, 
  Users, 
  TrendingUp, 
  TrendingDown,
  ArrowRight,
  Calendar
} from "lucide-react";
import { MetricCard } from "@/components/common/MetricCard";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/context/AuthContext";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { DataTable } from "@/components/common/DataTable";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getFirestore, collection, getDocs, query, where, orderBy, limit, Timestamp } from "firebase/firestore";
import { format, startOfWeek, endOfWeek, eachDayOfInterval, isToday, subDays, parseISO, startOfMonth, endOfMonth, subMonths, isAfter, isBefore } from "date-fns";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Sale, Product } from "@/types/firebase";

export default function Dashboard() {
  const { isAdmin, currentUser } = useAuth();
  const [salesData, setSalesData] = useState<{ day: string; sales: number }[]>([]);
  
  const { data: allSales = [], isLoading: isLoadingSales } = useQuery({
    queryKey: ["sales"],
    queryFn: async () => {
      try {
        const db = getFirestore();
        const salesCollection = collection(db, "sales");
        const querySnapshot = await getDocs(salesCollection);

        const sales = querySnapshot.docs.map(doc => {
          const data = doc.data();

          // Handle date properly
          let processedDate = '';
          if (data.timestamp && data.timestamp.seconds) {
            try {
              processedDate = format(new Date(data.timestamp.seconds * 1000), 'yyyy-MM-dd');
            } catch (error) {
              console.error("Error formatting timestamp:", error);
              processedDate = new Date().toISOString().split('T')[0];
            }
          } else if (data.date) {
            processedDate = data.date;
          } else {
            processedDate = new Date().toISOString().split('T')[0];
          }

          return {
            id: doc.id,
            ...data,
            date: processedDate,
            amount: Number(data.amount) || 0,
            items: Number(data.items) || 0,
            customer: data.customer || 'Walk-in Customer',
            status: data.status || 'completed',
            products: data.products || [],
            paymentMethod: data.paymentMethod || 'cash'
          };
        });

        console.log("Fetched sales data:", sales.length, "sales");
        console.log("Sample sale:", sales[0]);
        return sales as Sale[];
      } catch (error) {
        console.error("Error fetching sales:", error);
        toast.error("Failed to load sales data");
        return [];
      }
    }
  });

  const { data: products = [], isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products"],
    queryFn: async () => {
      try {
        const db = getFirestore();
        const productsCollection = collection(db, "products");
        const querySnapshot = await getDocs(productsCollection);

        const products = querySnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            name: data.name || 'Unnamed Product',
            category: data.category || 'Uncategorized',
            stock: Number(data.stock) || 0,
            sku: data.sku || '',
            threshold: Number(data.threshold) || 5,
            price: Number(data.sellingPrice || data.price) || 0,
            buyingPrice: Number(data.buyingPrice || data.price) || 0,
            sellingPrice: Number(data.sellingPrice || data.price) || 0,
            description: data.description || '',
            createdAt: data.createdAt || new Date().toISOString()
          };
        });

        console.log("Fetched products data:", products.length, "products");
        console.log("Sample product:", products[0]);
        return products as Product[];
      } catch (error) {
        console.error("Error fetching products:", error);
        toast.error("Failed to load product data");
        return [];
      }
    }
  });
  
  const recentSales = allSales && allSales.length > 0
    ? [...allSales]
        .sort((a, b) => {
          let dateA, dateB;

          // Handle date A
          if (a.timestamp && a.timestamp.seconds) {
            dateA = new Date(a.timestamp.seconds * 1000);
          } else if (a.date) {
            try {
              dateA = new Date(a.date);
              if (isNaN(dateA.getTime())) {
                dateA = new Date(0);
              }
            } catch (error) {
              dateA = new Date(0);
            }
          } else {
            dateA = new Date(0);
          }

          // Handle date B
          if (b.timestamp && b.timestamp.seconds) {
            dateB = new Date(b.timestamp.seconds * 1000);
          } else if (b.date) {
            try {
              dateB = new Date(b.date);
              if (isNaN(dateB.getTime())) {
                dateB = new Date(0);
              }
            } catch (error) {
              dateB = new Date(0);
            }
          } else {
            dateB = new Date(0);
          }

          return dateB.getTime() - dateA.getTime();
        })
        .slice(0, 5)
    : [];
  
  const lowStockItems = products && products.length > 0
    ? products
        .filter(product => (product.stock <= (product.threshold || 5)))
        .sort((a, b) => a.stock - b.stock)
        .slice(0, 5)
    : [];
  
  useEffect(() => {
    if (allSales && allSales.length > 0) {
      const last7Days = Array.from({ length: 7 }, (_, i) => subDays(new Date(), 6 - i));
      
      const formattedData = last7Days.map(date => ({
        day: format(date, 'EEE'),
        sales: 0,
        fullDate: date
      }));
      
      allSales.forEach(sale => {
        let saleDate;
        if (sale.timestamp && sale.timestamp.seconds) {
          saleDate = new Date(sale.timestamp.seconds * 1000);
        } else if (sale.date) {
          try {
            saleDate = new Date(sale.date);
            if (isNaN(saleDate.getTime())) {
              return;
            }
          } catch (error) {
            console.error("Invalid date format:", sale.date);
            return;
          }
        } else {
          return;
        }
        
        const dayIndex = formattedData.findIndex(item => 
          format(item.fullDate, 'yyyy-MM-dd') === format(saleDate, 'yyyy-MM-dd')
        );
        
        if (dayIndex !== -1) {
          formattedData[dayIndex].sales += sale.amount || 0;
        }
      });
      
      setSalesData(formattedData.map(({ day, sales }) => ({ day, sales })));
    }
  }, [allSales]);
  
  const getTodaySales = () => {
    let totalAmount = 0;
    let count = 0;
    
    if (!allSales || allSales.length === 0) return { amount: 0, count: 0 };
    
    allSales.forEach(sale => {
      let saleDate;
      if (sale.timestamp && sale.timestamp.seconds) {
        saleDate = new Date(sale.timestamp.seconds * 1000);
      } else if (sale.date) {
        try {
          saleDate = new Date(sale.date);
          if (isNaN(saleDate.getTime())) {
            return;
          }
        } catch (error) {
          console.error("Invalid date format:", sale.date);
          return;
        }
      } else {
        return;
      }
      
      if (isToday(saleDate)) {
        totalAmount += (sale.amount || 0);
        count++;
      }
    });
    
    return { amount: totalAmount, count };
  };
  
  const getLowStockCount = () => {
    return products ? products.filter(product => product.stock <= (product.threshold || 5)).length : 0;
  };
  
  const getPendingOrders = () => {
    return allSales ? allSales.filter(sale => sale.status === 'pending').length : 0;
  };
  
  const getTodayCustomers = () => {
    if (!allSales || allSales.length === 0) return 0;
    
    const todayCustomers = new Set();
    
    allSales.forEach(sale => {
      let saleDate;
      if (sale.timestamp && sale.timestamp.seconds) {
        saleDate = new Date(sale.timestamp.seconds * 1000);
      } else if (sale.date) {
        try {
          saleDate = new Date(sale.date);
          if (isNaN(saleDate.getTime())) {
            return;
          }
        } catch (error) {
          console.error("Invalid date format:", sale.date);
          return;
        }
      } else {
        return;
      }
      
      if (isToday(saleDate) && sale.customer) {
        todayCustomers.add(sale.customer);
      }
    });
    
    return todayCustomers.size;
  };
  
  const todaySales = getTodaySales();
  const lowStockCount = getLowStockCount();
  const pendingOrders = getPendingOrders();
  const todayCustomers = getTodayCustomers();

  // Debug logging
  console.log("Dashboard metrics:", {
    todaySales,
    lowStockCount,
    pendingOrders,
    todayCustomers,
    totalSales: allSales?.length || 0,
    totalProducts: products?.length || 0
  });

  // Calculate real trend data
  const calculateTrend = (current: number, previous: number): { value: number; positive: boolean } => {
    if (previous === 0) return { value: 0, positive: true };
    const change = ((current - previous) / previous) * 100;
    return { value: Math.abs(change), positive: change >= 0 };
  };

  // Get previous period data for trends
  const getPreviousWeekSales = () => {
    if (!allSales) return 0;
    const oneWeekAgo = subDays(new Date(), 7);
    const twoWeeksAgo = subDays(new Date(), 14);
    return allSales.filter(sale => {
      let saleDate;
      if (sale.timestamp && sale.timestamp.seconds) {
        saleDate = new Date(sale.timestamp.seconds * 1000);
      } else if (sale.date) {
        try {
          saleDate = new Date(sale.date);
          if (isNaN(saleDate.getTime())) {
            return false;
          }
        } catch (error) {
          console.error("Invalid date format:", sale.date);
          return false;
        }
      } else {
        return false;
      }
      return saleDate >= twoWeeksAgo && saleDate < oneWeekAgo;
    }).reduce((sum, sale) => sum + (sale.amount || 0), 0);
  };

  const getPreviousMonthRevenue = () => {
    if (!allSales) return 0;
    const oneMonthAgo = subDays(new Date(), 30);
    const twoMonthsAgo = subDays(new Date(), 60);
    return allSales.filter(sale => {
      let saleDate;
      if (sale.timestamp && sale.timestamp.seconds) {
        saleDate = new Date(sale.timestamp.seconds * 1000);
      } else if (sale.date) {
        try {
          saleDate = new Date(sale.date);
          if (isNaN(saleDate.getTime())) {
            return false;
          }
        } catch (error) {
          console.error("Invalid date format:", sale.date);
          return false;
        }
      } else {
        return false;
      }
      return saleDate >= twoMonthsAgo && saleDate < oneMonthAgo;
    }).reduce((sum, sale) => sum + (sale.amount || 0), 0);
  };

  const currentMonthRevenue = allSales ? allSales.reduce((sum, sale) => sum + (sale.amount || 0), 0) : 0;
  const previousMonthRevenue = getPreviousMonthRevenue();
  const currentWeekSales = allSales ? allSales.filter(sale => {
    let saleDate;
    if (sale.timestamp && sale.timestamp.seconds) {
      saleDate = new Date(sale.timestamp.seconds * 1000);
    } else if (sale.date) {
      try {
        saleDate = new Date(sale.date);
        if (isNaN(saleDate.getTime())) {
          return false;
        }
      } catch (error) {
        console.error("Invalid date format:", sale.date);
        return false;
      }
    } else {
      return false;
    }
    return saleDate >= subDays(new Date(), 7);
  }).length : 0;
  const previousWeekSales = allSales ? allSales.filter(sale => {
    let saleDate;
    if (sale.timestamp && sale.timestamp.seconds) {
      saleDate = new Date(sale.timestamp.seconds * 1000);
    } else if (sale.date) {
      try {
        saleDate = new Date(sale.date);
        if (isNaN(saleDate.getTime())) {
          return false;
        }
      } catch (error) {
        console.error("Invalid date format:", sale.date);
        return false;
      }
    } else {
      return false;
    }
    const oneWeekAgo = subDays(new Date(), 7);
    const twoWeeksAgo = subDays(new Date(), 14);
    return saleDate >= twoWeeksAgo && saleDate < oneWeekAgo;
  }).length : 0;

  const yesterdaySales = allSales ? allSales.filter(sale => {
    let saleDate;
    if (sale.timestamp && sale.timestamp.seconds) {
      saleDate = new Date(sale.timestamp.seconds * 1000);
    } else if (sale.date) {
      try {
        saleDate = new Date(sale.date);
        if (isNaN(saleDate.getTime())) {
          return false;
        }
      } catch (error) {
        console.error("Invalid date format:", sale.date);
        return false;
      }
    } else {
      return false;
    }

    const yesterday = subDays(new Date(), 1);
    return format(saleDate, 'yyyy-MM-dd') === format(yesterday, 'yyyy-MM-dd');
  }).reduce((sum, sale) => sum + (sale.amount || 0), 0) : 0;

  // Profit calculation functions
  const calculateProfitForSale = (sale: Sale) => {
    let totalProfit = 0;

    sale.products?.forEach(item => {
      const product = products?.find(p => p.id === item.productId);
      if (product) {
        // Use buyingPrice if available, otherwise estimate as 70% of selling price
        const costPrice = product.buyingPrice || (product.sellingPrice || product.price) * 0.7;
        const sellingPrice = item.price;
        const profit = (sellingPrice - costPrice) * item.quantity;
        totalProfit += profit;
      }
    });

    return totalProfit;
  };

  // Calculate monthly profit
  const currentMonthProfit = allSales ? allSales.filter(sale => {
    let saleDate;
    if (sale.timestamp && sale.timestamp.seconds) {
      saleDate = new Date(sale.timestamp.seconds * 1000);
    } else if (sale.date) {
      try {
        saleDate = new Date(sale.date);
        if (isNaN(saleDate.getTime())) {
          return false;
        }
      } catch (error) {
        return false;
      }
    } else {
      return false;
    }

    const currentMonth = startOfMonth(new Date());
    const endOfCurrentMonth = endOfMonth(new Date());
    return saleDate >= currentMonth && saleDate <= endOfCurrentMonth;
  }).reduce((sum, sale) => sum + calculateProfitForSale(sale), 0) : 0;

  // Calculate previous month profit for trend
  const previousMonthProfit = allSales ? allSales.filter(sale => {
    let saleDate;
    if (sale.timestamp && sale.timestamp.seconds) {
      saleDate = new Date(sale.timestamp.seconds * 1000);
    } else if (sale.date) {
      try {
        saleDate = new Date(sale.date);
        if (isNaN(saleDate.getTime())) {
          return false;
        }
      } catch (error) {
        return false;
      }
    } else {
      return false;
    }

    const previousMonth = startOfMonth(subMonths(new Date(), 1));
    const endOfPreviousMonth = endOfMonth(subMonths(new Date(), 1));
    return saleDate >= previousMonth && saleDate <= endOfPreviousMonth;
  }).reduce((sum, sale) => sum + calculateProfitForSale(sale), 0) : 0;

  const adminMetrics = [
    {
      title: "Monthly Revenue",
      value: `Tsh${currentMonthRevenue.toFixed(2)}`,
      description: "This month",
      icon: <DollarSign />,
      trend: previousMonthRevenue > 0 ? calculateTrend(currentMonthRevenue, previousMonthRevenue) : undefined
    },
    {
      title: "Monthly Profit",
      value: `Tsh${currentMonthProfit.toFixed(2)}`,
      description: "This month",
      icon: <DollarSign className="text-green-600" />,
      trend: previousMonthProfit > 0 ? calculateTrend(currentMonthProfit, previousMonthProfit) : undefined
    },
    {
      title: "Sales",
      value: currentWeekSales.toString(),
      description: "This week",
      icon: <ShoppingCart />,
      trend: previousWeekSales > 0 ? calculateTrend(currentWeekSales, previousWeekSales) : undefined
    },
    {
      title: "Stationery Items",
      value: products ? products.length.toString() : "0",
      description: "In inventory",
      icon: <Package />,
      trend: undefined // No meaningful trend for inventory count
    },
  ];

  // Calculate today's profit
  const todaysProfit = allSales ? allSales.filter(sale => {
    let saleDate;
    if (sale.timestamp && sale.timestamp.seconds) {
      saleDate = new Date(sale.timestamp.seconds * 1000);
    } else if (sale.date) {
      try {
        saleDate = new Date(sale.date);
        if (isNaN(saleDate.getTime())) {
          return false;
        }
      } catch (error) {
        return false;
      }
    } else {
      return false;
    }

    return isToday(saleDate);
  }).reduce((sum, sale) => sum + calculateProfitForSale(sale), 0) : 0;

  const shopkeeperMetrics = [
    {
      title: "Today's Sales",
      value: `Tsh${todaySales.amount.toFixed(2)}`,
      description: `${todaySales.count} transactions`,
      icon: <ShoppingCart />,
      trend: yesterdaySales > 0 ? calculateTrend(todaySales.amount, yesterdaySales) : undefined
    },
    {
      title: "Today's Profit",
      value: `Tsh${todaysProfit.toFixed(2)}`,
      description: "Profit earned today",
      icon: <DollarSign className="text-green-600" />,
      trend: undefined // No historical data for daily profit comparison
    },
    {
      title: "Low Stock Items",
      value: lowStockCount.toString(),
      description: "Need restocking",
      icon: <Package />,
      trend: undefined // No meaningful trend for low stock count
    },
    {
      title: "Today's Customers",
      value: todayCustomers.toString(),
      description: "Store visitors",
      icon: <Users />,
      trend: undefined // No historical data for daily customers
    },
  ];
  
  const metrics = isAdmin() ? adminMetrics : adminMetrics;

  const totalSales = salesData.reduce((sum, day) => sum + day.sales, 0);
  const hasWeeklyChange = salesData.length > 0;

  // Calculate weekly change percentage for chart
  const calculateWeeklyChange = () => {
    if (!allSales || allSales.length === 0) return { percentage: 0, positive: true };

    const thisWeekSales = allSales.filter(sale => {
      let saleDate;
      if (sale.timestamp && sale.timestamp.seconds) {
        saleDate = new Date(sale.timestamp.seconds * 1000);
      } else if (sale.date) {
        try {
          saleDate = new Date(sale.date);
          if (isNaN(saleDate.getTime())) {
            return false;
          }
        } catch (error) {
          console.error("Invalid date format:", sale.date);
          return false;
        }
      } else {
        return false;
      }
      return saleDate >= subDays(new Date(), 7);
    }).reduce((sum, sale) => sum + (sale.amount || 0), 0);

    const lastWeekSales = allSales.filter(sale => {
      let saleDate;
      if (sale.timestamp && sale.timestamp.seconds) {
        saleDate = new Date(sale.timestamp.seconds * 1000);
      } else if (sale.date) {
        try {
          saleDate = new Date(sale.date);
          if (isNaN(saleDate.getTime())) {
            return false;
          }
        } catch (error) {
          console.error("Invalid date format:", sale.date);
          return false;
        }
      } else {
        return false;
      }
      const oneWeekAgo = subDays(new Date(), 7);
      const twoWeeksAgo = subDays(new Date(), 14);
      return saleDate >= twoWeeksAgo && saleDate < oneWeekAgo;
    }).reduce((sum, sale) => sum + (sale.amount || 0), 0);

    if (lastWeekSales === 0) return { percentage: 0, positive: true };

    const change = ((thisWeekSales - lastWeekSales) / lastWeekSales) * 100;
    return { percentage: Math.abs(change), positive: change >= 0 };
  };

  const weeklyChange = calculateWeeklyChange();

  const formatDateForDisplay = (sale: any) => {
    if (!sale) return 'Unknown';
    
    if (sale.timestamp && sale.timestamp.seconds) {
      return format(new Date(sale.timestamp.seconds * 1000), 'yyyy-MM-dd');
    } else if (sale.date) {
      try {
        return format(new Date(sale.date), 'yyyy-MM-dd');
      } catch (error) {
        console.error("Invalid date format:", sale.date);
        return sale.date || 'Unknown';
      }
    }
    
    return 'Unknown';
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{isAdmin() ? 'Store Manager Dashboard' : 'Stationery Shop Dashboard'}</h1>
        <p className="text-muted-foreground">
          Welcome to your {isAdmin() ? 'store management' : 'stationery shop'} dashboard, here's what's happening today.
        </p>
      </div>

      <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            description={metric.description}
            icon={metric.icon}
            trend={metric.trend}
            className="scale-in"
            style={{ animationDelay: `${index * 100}ms` }}
          />
        ))}
      </div>

      <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-3">
        <Card className="lg:col-span-2">
          <CardHeader className="flex flex-col space-y-2 pb-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div>
              <CardTitle className="text-lg sm:text-xl">Sales Overview</CardTitle>
              <CardDescription>Daily sales for the last week</CardDescription>
            </div>
            {isAdmin() && (
              <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                <Link to="/reports">
                  <span className="sm:hidden">Reports</span>
                  <span className="hidden sm:inline">View Reports</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            )}
          </CardHeader>
          <CardContent className="pt-4">
            <div className="h-[250px] sm:h-[300px]">
              {isLoadingSales ? (
                <div className="flex items-center justify-center h-full">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : salesData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={salesData} margin={{ top: 5, right: 10, left: 0, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey="day" fontSize={12} />
                    <YAxis fontSize={12} />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'white',
                        borderRadius: '8px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                        border: 'none',
                        fontSize: '12px'
                      }}
                      formatter={(value) => [`Tsh ${value}`, 'Sales']}
                    />
                    <Bar dataKey="sales" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-muted-foreground text-sm">No sales data available</p>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4">
            <div className="flex flex-col gap-2 w-full sm:flex-row sm:items-center sm:justify-between">
              <div className="flex items-center text-xs sm:text-sm text-muted-foreground">
                {hasWeeklyChange ? (
                  <>
                    {weeklyChange.positive ? (
                      <TrendingUp className="mr-1 h-3 w-3 sm:h-4 sm:w-4 text-green-500" />
                    ) : (
                      <TrendingDown className="mr-1 h-3 w-3 sm:h-4 sm:w-4 text-red-500" />
                    )}
                    <span className={`font-medium ${weeklyChange.positive ? 'text-green-500' : 'text-red-500'}`}>
                      {weeklyChange.positive ? '+' : '-'}{weeklyChange.percentage.toFixed(1)}%
                    </span>
                    <span className="ml-1 hidden sm:inline">from last week</span>
                    <span className="ml-1 sm:hidden">vs last week</span>
                  </>
                ) : (
                  <span>Not enough data</span>
                )}
              </div>
              <div className="text-xs sm:text-sm font-medium">
                Total: Tsh{totalSales.toFixed(2)}
              </div>
            </div>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg sm:text-xl">Recent Activity</CardTitle>
            <CardDescription>Latest updates and actions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {isLoadingSales || isLoadingProducts ? (
                <div className="flex justify-center py-4">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : (
                <>
                  {recentSales.length > 0 && (
                    <div className="flex items-center gap-2 sm:gap-3 rounded-lg border p-2 sm:p-3">
                      <div className="flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-full bg-background">
                        <ShoppingCart className="h-3 w-3 sm:h-4 sm:w-4 text-green-500" />
                      </div>
                      <div className="text-xs sm:text-sm">New sale completed - Tsh {recentSales[0].amount?.toFixed(2)}</div>
                    </div>
                  )}

                  {lowStockItems.length > 0 && (
                    <div className="flex items-center gap-2 sm:gap-3 rounded-lg border p-2 sm:p-3">
                      <div className="flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-full bg-background">
                        <Package className="h-3 w-3 sm:h-4 sm:w-4 text-amber-500" />
                      </div>
                      <div className="text-xs sm:text-sm">{lowStockItems[0].name} low in stock - {lowStockItems[0].stock} remaining</div>
                    </div>
                  )}

                  {pendingOrders > 0 && (
                    <div className="flex items-center gap-2 sm:gap-3 rounded-lg border p-2 sm:p-3">
                      <div className="flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-full bg-background">
                        <TrendingDown className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500" />
                      </div>
                      <div className="text-xs sm:text-sm">{pendingOrders} pending orders need processing</div>
                    </div>
                  )}

                  {lowStockItems.filter(item => item.stock === 0).length > 0 && (
                    <div className="flex items-center gap-2 sm:gap-3 rounded-lg border p-2 sm:p-3">
                      <div className="flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-full bg-background">
                        <TrendingDown className="h-3 w-3 sm:h-4 sm:w-4 text-red-500" />
                      </div>
                      <div className="text-xs sm:text-sm">{lowStockItems.filter(item => item.stock === 0).length} products out of stock</div>
                    </div>
                  )}

                  {todaySales.count > 0 && (
                    <div className="flex items-center gap-2 sm:gap-3 rounded-lg border p-2 sm:p-3">
                      <div className="flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-full bg-background">
                        <Users className="h-3 w-3 sm:h-4 sm:w-4 text-indigo-500" />
                      </div>
                      <div className="text-xs sm:text-sm">{todayCustomers} customers visited today</div>
                    </div>
                  )}
                </>
              )}
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4">
            <Button variant="outline" size="sm" className="w-full text-xs sm:text-sm" asChild>
              <Link to="/activities">
                View All Activity
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div>
              <CardTitle className="text-lg sm:text-xl">Recent Sales</CardTitle>
              <CardDescription>Latest sales transactions</CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
              <Link to="/sales">
                <span className="sm:hidden">All Sales</span>
                <span className="hidden sm:inline">View All</span>
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <DataTable
              data={recentSales}
              columns={[
                {
                  id: "date",
                  header: "Date",
                  cell: (row: any) => {
                    return (
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>{formatDateForDisplay(row)}</span>
                      </div>
                    );
                  }
                },
                {
                  id: "customer",
                  header: "Customer",
                  cell: (row: any) => <div className="font-medium">{row.customer || 'Unknown'}</div>,
                },
                {
                  id: "amount",
                  header: "Amount",
                  cell: (row: any) => (
                    <div className="font-medium text-right">
                      Tsh {row.amount?.toFixed(2) || '0.00'}
                    </div>
                  ),
                },
              ]}
              isLoading={isLoadingSales}
              emptyMessage="No recent sales"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div>
              <CardTitle className="text-lg sm:text-xl">Low Stock Items</CardTitle>
              <CardDescription>Items that need attention</CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
              <Link to="/inventory">
                <span className="sm:hidden">Inventory</span>
                <span className="hidden sm:inline">View Inventory</span>
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <DataTable
              data={lowStockItems}
              columns={[
                {
                  id: "name",
                  header: "Product",
                  cell: (row: any) => <div className="font-medium">{row.name || 'Unnamed Product'}</div>,
                },
                {
                  id: "category",
                  header: "Category",
                  cell: (row: any) => <div>{row.category || 'Uncategorized'}</div>,
                },
                {
                  id: "stock",
                  header: "Stock",
                  cell: (row: any) => (
                    <div className={`font-medium ${row.stock === 0 ? 'text-red-500' : row.stock < (row.threshold || 5) ? 'text-amber-500' : ''}`}>
                      {row.stock || 0}
                    </div>
                  ),
                },
                {
                  id: "threshold",
                  header: "Threshold",
                  cell: (row: any) => <div>{row.threshold || 5}</div>,
                },
              ]}
              isLoading={isLoadingProducts}
              emptyMessage="No low stock items"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}


