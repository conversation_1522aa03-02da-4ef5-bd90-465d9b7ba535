import { useEffect, useRef } from 'react';
import { expenseService } from '@/lib/firebase';
import { toast } from 'sonner';

export const useRecurringExpenses = () => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isProcessingRef = useRef(false);

  const processRecurringExpenses = async () => {
    // Prevent multiple simultaneous processing
    if (isProcessingRef.current) {
      return;
    }

    try {
      isProcessingRef.current = true;
      console.log('Processing recurring expenses...');
      
      const processedExpenses = await expenseService.processRecurringExpenses();
      
      if (processedExpenses.length > 0) {
        console.log(`Processed ${processedExpenses.length} recurring expenses`);
        toast.success(`Generated ${processedExpenses.length} recurring expense(s)`);
      }
    } catch (error) {
      console.error('Error processing recurring expenses:', error);
      toast.error('Failed to process recurring expenses');
    } finally {
      isProcessingRef.current = false;
    }
  };

  useEffect(() => {
    // Process recurring expenses immediately on mount
    processRecurringExpenses();

    // Set up interval to check for recurring expenses every hour
    intervalRef.current = setInterval(() => {
      processRecurringExpenses();
    }, 60 * 60 * 1000); // 1 hour

    // Cleanup interval on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Return a manual trigger function for testing or manual processing
  return {
    processRecurringExpenses
  };
};
