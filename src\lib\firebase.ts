import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  onAuthStateChanged
} from 'firebase/auth';
import { 
  getFirestore, 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  onSnapshot,
  query,
  where,
  serverTimestamp,
  DocumentData
} from 'firebase/firestore';
import type { Product, FirestoreUser, Expense } from '@/types/firebase';

// Firebase configuration
export const firebaseConfig = {
  apiKey: "AIzaSyBWzgY7yMJgVRaOeZru5WTwWpRzLSC4PF8",
  authDomain: "zdstationary.firebaseapp.com",
  projectId: "zdstationary",
  storageBucket: "zdstationary.firebasestorage.app",
  messagingSenderId: "736271901401",
  appId: "1:736271901401:web:ebbbbec14a07ee56ef14c8",
  measurementId: "G-MPRLR0XEBV"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const firebaseAuth = getAuth(app);
export const firebaseFirestore = getFirestore(app); // Added export

// Auth service
export const auth = {
  currentUser: firebaseAuth.currentUser,
  async signIn(email: string, password: string) {
    try {
      const userCredential = await signInWithEmailAndPassword(firebaseAuth, email, password);
      const userDoc = await getDoc(doc(firebaseFirestore, 'users', userCredential.user.uid));
      const userData = userDoc.exists() ? userDoc.data() as FirestoreUser : null;
      
      return { 
        user: { 
          uid: userCredential.user.uid, 
          email: userCredential.user.email, 
          displayName: userCredential.user.displayName || userData?.displayName || email.split('@')[0],
          role: userData?.role || 'shopkeeper'
        } 
      };
    } catch (error) {
      console.error("Error signing in:", error);
      throw error;
    }
  },
  async signOut() {
    try {
      await firebaseSignOut(firebaseAuth);
      return true;
    } catch (error) {
      console.error("Error signing out:", error);
      throw error;
    }
  },
  onAuthStateChanged,
};

// Firestore service
export const firestore = {
  collection: (name: string) => ({
    get: async () => {
      try {
        const querySnapshot = await getDocs(collection(firebaseFirestore, name));
        return {
          docs: querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })),
        };
      } catch (error) {
        console.error(`Error getting collection ${name}:`, error);
        throw error;
      }
    },
    add: async (data: DocumentData) => {
      try {
        const docRef = await addDoc(collection(firebaseFirestore, name), {
          ...data,
          createdAt: serverTimestamp(),
        });
        return { id: docRef.id };
      } catch (error) {
        console.error(`Error adding document to ${name}:`, error);
        throw error;
      }
    },
    doc: (id: string) => ({
      get: async () => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          const docSnap = await getDoc(docRef);
          return {
            exists: docSnap.exists(),
            data: () => ({ id: docSnap.id, ...docSnap.data() }),
          };
        } catch (error) {
          console.error(`Error getting document ${name}/${id}:`, error);
          throw error;
        }
      },
      update: async (data: DocumentData) => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          await updateDoc(docRef, data);
          return true;
        } catch (error) {
          console.error(`Error updating document ${name}/${id}:`, error);
          throw error;
        }
      },
      delete: async () => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          await deleteDoc(docRef);
          return true;
        } catch (error) {
          console.error(`Error deleting document ${name}/${id}:`, error);
          throw error;
        }
      },
    }),
    onSnapshot: (callback: (data: DocumentData[]) => void) => {
      const q = collection(firebaseFirestore, name);
      return onSnapshot(q, (querySnapshot) => {
        const items = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        callback(items);
      });
    },
    where: (field: string, operator: any, value: any) => {
      const q = query(
        collection(firebaseFirestore, name),
        where(field, operator, value)
      );
      return {
        get: async () => {
          try {
            const querySnapshot = await getDocs(q);
            return {
              docs: querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
              })),
            };
          } catch (error) {
            console.error(`Error querying collection ${name}:`, error);
            throw error;
          }
        },
        onSnapshot: (callback: (data: DocumentData[]) => void) => {
          return onSnapshot(q, (querySnapshot) => {
            const items = querySnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));
            callback(items);
          });
        }
      };
    }
  }),
};

// Expense management functions
export const expenseService = {
  // Get all expenses
  getAll: async () => {
    try {
      const querySnapshot = await getDocs(collection(firebaseFirestore, 'expenses'));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Expense[];
    } catch (error) {
      console.error('Error getting expenses:', error);
      throw error;
    }
  },

  // Get expenses by user
  getByUser: async (userId: string) => {
    try {
      const q = query(
        collection(firebaseFirestore, 'expenses'),
        where('createdBy', '==', userId)
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Expense[];
    } catch (error) {
      console.error('Error getting user expenses:', error);
      throw error;
    }
  },

  // Get expenses by status
  getByStatus: async (status: 'pending' | 'approved' | 'rejected') => {
    try {
      const q = query(
        collection(firebaseFirestore, 'expenses'),
        where('status', '==', status)
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Expense[];
    } catch (error) {
      console.error('Error getting expenses by status:', error);
      throw error;
    }
  },

  // Add new expense
  add: async (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const docRef = await addDoc(collection(firebaseFirestore, 'expenses'), {
        ...expenseData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      return { id: docRef.id };
    } catch (error) {
      console.error('Error adding expense:', error);
      throw error;
    }
  },

  // Update expense
  update: async (id: string, expenseData: Partial<Expense>) => {
    try {
      const docRef = doc(firebaseFirestore, 'expenses', id);
      await updateDoc(docRef, {
        ...expenseData,
        updatedAt: serverTimestamp(),
      });
      return true;
    } catch (error) {
      console.error('Error updating expense:', error);
      throw error;
    }
  },

  // Delete expense
  delete: async (id: string) => {
    try {
      const docRef = doc(firebaseFirestore, 'expenses', id);
      await deleteDoc(docRef);
      return true;
    } catch (error) {
      console.error('Error deleting expense:', error);
      throw error;
    }
  },

  // Get expense by ID
  getById: async (id: string) => {
    try {
      const docRef = doc(firebaseFirestore, 'expenses', id);
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Expense;
      }
      return null;
    } catch (error) {
      console.error('Error getting expense by ID:', error);
      throw error;
    }
  },

  // Get recurring expenses that need to be processed
  getRecurringExpenses: async () => {
    try {
      const q = query(
        collection(firebaseFirestore, 'expenses'),
        where('isRecurring', '==', true)
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Expense[];
    } catch (error) {
      console.error('Error getting recurring expenses:', error);
      throw error;
    }
  },

  // Process recurring expenses - generate new instances based on frequency
  processRecurringExpenses: async () => {
    try {
      const recurringExpenses = await expenseService.getRecurringExpenses();
      const now = new Date();
      const processedExpenses = [];

      for (const expense of recurringExpenses) {
        const lastDate = new Date(expense.date);
        const nextDueDate = expenseService.calculateNextDueDate(lastDate, expense.recurringFrequency);

        // If the next due date has passed, create a new expense instance
        if (nextDueDate <= now) {
          const newExpenseData = {
            ...expense,
            date: nextDueDate.toISOString().split('T')[0],
            id: undefined, // Remove the ID so a new one is generated
            createdAt: undefined,
            updatedAt: undefined,
            status: 'pending' as const // New recurring instances start as pending
          };

          // Remove the id field completely
          delete newExpenseData.id;
          delete newExpenseData.createdAt;
          delete newExpenseData.updatedAt;

          const result = await expenseService.add(newExpenseData);

          // Update the original recurring expense's date to the new date
          await expenseService.update(expense.id, {
            date: nextDueDate.toISOString().split('T')[0]
          });

          processedExpenses.push({
            originalId: expense.id,
            newId: result.id,
            nextDueDate: nextDueDate
          });
        }
      }

      return processedExpenses;
    } catch (error) {
      console.error('Error processing recurring expenses:', error);
      throw error;
    }
  },

  // Calculate the next due date based on frequency
  calculateNextDueDate: (lastDate: Date, frequency: 'daily' | 'weekly' | 'monthly' | 'yearly') => {
    const nextDate = new Date(lastDate);

    switch (frequency) {
      case 'daily':
        nextDate.setDate(nextDate.getDate() + 1);
        break;
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case 'yearly':
        nextDate.setFullYear(nextDate.getFullYear() + 1);
        break;
      default:
        nextDate.setMonth(nextDate.getMonth() + 1); // Default to monthly
    }

    return nextDate;
  },

  // Calculate total expenses
  calculateTotal: async (filters?: {
    startDate?: string;
    endDate?: string;
    category?: string;
    status?: string;
  }) => {
    try {
      let q = collection(firebaseFirestore, 'expenses');

      if (filters?.status) {
        q = query(q, where('status', '==', filters.status)) as any;
      }

      if (filters?.category) {
        q = query(q, where('category', '==', filters.category)) as any;
      }

      const querySnapshot = await getDocs(q);
      const expenses = querySnapshot.docs.map(doc => doc.data()) as Expense[];

      // Filter by date range if provided
      let filteredExpenses = expenses;
      if (filters?.startDate || filters?.endDate) {
        filteredExpenses = expenses.filter(expense => {
          const expenseDate = new Date(expense.date);
          if (filters.startDate && expenseDate < new Date(filters.startDate)) return false;
          if (filters.endDate && expenseDate > new Date(filters.endDate)) return false;
          return true;
        });
      }

      return filteredExpenses.reduce((total, expense) => total + expense.amount, 0);
    } catch (error) {
      console.error('Error calculating total expenses:', error);
      throw error;
    }
  },

  // Get expense categories with totals
  getCategorySummary: async () => {
    try {
      const querySnapshot = await getDocs(collection(firebaseFirestore, 'expenses'));
      const expenses = querySnapshot.docs.map(doc => doc.data()) as Expense[];

      const categorySummary: { [key: string]: { total: number; count: number } } = {};

      expenses.forEach(expense => {
        if (!categorySummary[expense.category]) {
          categorySummary[expense.category] = { total: 0, count: 0 };
        }
        categorySummary[expense.category].total += expense.amount;
        categorySummary[expense.category].count += 1;
      });

      return categorySummary;
    } catch (error) {
      console.error('Error getting category summary:', error);
      throw error;
    }
  }
};

// Create demo users in Firebase
export const createDemoData = async () => {
  try {
    
    
   

    // Check if we already have users
    const usersSnapshot = await getDocs(collection(firebaseFirestore, 'users'));
    if (usersSnapshot.empty) {
      const demoUsers = [
        { email: '<EMAIL>', displayName: 'Store Manager', role: 'admin' },
        { email: '<EMAIL>', displayName: 'Sales Associate', role: 'shopkeeper' },
      ];
      
      for (const user of demoUsers) {
        await addDoc(collection(firebaseFirestore, 'users'), {
          ...user,
          createdAt: serverTimestamp(),
        });
      }
      console.log('Demo user documents created');
    }


  
  } catch (error) {
    console.error('Error creating demo data:', error);
  }
};

// Initialize demo data
createDemoData();

export default { auth, firestore, expenseService, createDemoData };