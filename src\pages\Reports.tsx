
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format, startOfMonth, endOfMonth, subMonths, isAfter, isBefore } from "date-fns";
import {
  Calendar as CalendarIcon,
  Download,
  FileText,
  FileCog,
  BarChart3,
  PieChart,
  TrendingUp,
  DollarSign,
  Package,
  ShoppingCart,
  Receipt
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getFirestore, collection, getDocs, query, where, orderBy, Timestamp } from "firebase/firestore";
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart as RePieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts";
import { Sale, Product, Expense } from "@/types/firebase";
import { expenseService } from "@/lib/firebase";

// Colors for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d', '#ffc658'];

export default function Reports() {
  const [activeTab, setActiveTab] = useState("overview");
  const [reportType, setReportType] = useState("monthly");
  const [dateRange, setDateRange] = useState({
    from: startOfMonth(subMonths(new Date(), 1)),
    to: endOfMonth(new Date()),
  });
  const [category, setCategory] = useState("all");
  const { toast } = useToast();

  // Format date for display
  const formatDate = (date: Date) => {
    return format(date, "PPP");
  };

  // Query for sales data
  const { data: salesData = [], isLoading: isLoadingSales } = useQuery<Sale[]>({
    queryKey: ["sales", reportType, dateRange, category],
    queryFn: async () => {
      const db = getFirestore();
      const salesCollection = collection(db, "sales");

      let salesQuery = query(salesCollection, orderBy("timestamp", "desc"));

      const snapshot = await getDocs(salesQuery);
      const allSales = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Sale[];

      // Filter by date range and category in memory for more flexibility
      return allSales.filter(sale => {
        const saleDate = sale.timestamp?.toDate ? sale.timestamp.toDate() : new Date(sale.date);
        const isInDateRange = (!dateRange.from || isAfter(saleDate, dateRange.from) || saleDate.toDateString() === dateRange.from.toDateString()) &&
                             (!dateRange.to || isBefore(saleDate, dateRange.to) || saleDate.toDateString() === dateRange.to.toDateString());

        const isInCategory = category === "all" ||
                           (sale.products && sale.products.some(item => {
                             // We'll need to check product category from products collection
                             return true; // For now, include all
                           }));

        return isInDateRange && isInCategory;
      });
    }
  });

  // Query for inventory data
  const { data: inventoryData = [], isLoading: isLoadingInventory } = useQuery<Product[]>({
    queryKey: ["inventory", category],
    queryFn: async () => {
      const db = getFirestore();
      const productsCollection = collection(db, "products");

      let productsQuery = query(productsCollection, orderBy("name"));

      const snapshot = await getDocs(productsQuery);
      const allProducts = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Product[];

      // Filter by category
      if (category !== "all") {
        return allProducts.filter(product => product.category === category);
      }

      return allProducts;
    }
  });

  // Query for expenses data
  const { data: expensesData = [], isLoading: isLoadingExpenses } = useQuery<Expense[]>({
    queryKey: ["expenses", dateRange],
    queryFn: async () => {
      const allExpenses = await expenseService.getAll();

      // Filter by date range
      return allExpenses.filter(expense => {
        const expenseDate = new Date(expense.date);
        const isInDateRange = (!dateRange.from || isAfter(expenseDate, dateRange.from) || expenseDate.toDateString() === dateRange.from.toDateString()) &&
                             (!dateRange.to || isBefore(expenseDate, dateRange.to) || expenseDate.toDateString() === dateRange.to.toDateString());
        return isInDateRange;
      });
    }
  });

  // Query for product categories
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery<string[]>({
    queryKey: ["categories"],
    queryFn: async () => {
      const db = getFirestore();
      const productsCollection = collection(db, "products");
      const snapshot = await getDocs(productsCollection);

      // Extract unique categories
      const uniqueCategories = new Set<string>();
      snapshot.docs.forEach(doc => {
        const category = doc.data().category;
        if (category) uniqueCategories.add(category);
      });

      return Array.from(uniqueCategories);
    }
  });

  // Calculate comprehensive metrics
  const calculateMetrics = () => {
    const totalSales = salesData.reduce((sum, sale) => sum + (sale.amount || 0), 0);
    const totalExpenses = expensesData.reduce((sum, expense) => sum + (expense.amount || 0), 0);
    const totalItems = salesData.reduce((sum, sale) => sum + (sale.items || 0), 0);
    const averageSale = salesData.length > 0 ? totalSales / salesData.length : 0;
    const profit = totalSales - totalExpenses;
    const profitMargin = totalSales > 0 ? (profit / totalSales) * 100 : 0;

    // Low stock items
    const lowStockItems = inventoryData.filter(product =>
      product.stock <= (product.threshold || 10)
    ).length;

    // Top selling products
    const productSales: { [key: string]: { name: string; quantity: number; revenue: number } } = {};
    salesData.forEach(sale => {
      if (sale.products) {
        sale.products.forEach(item => {
          if (!productSales[item.productId]) {
            productSales[item.productId] = {
              name: item.productName,
              quantity: 0,
              revenue: 0
            };
          }
          productSales[item.productId].quantity += item.quantity;
          productSales[item.productId].revenue += item.quantity * item.price;
        });
      }
    });

    const topProducts = Object.values(productSales)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    return {
      totalSales,
      totalExpenses,
      totalItems,
      averageSale,
      profit,
      profitMargin,
      lowStockItems,
      topProducts
    };
  };

  const metrics = calculateMetrics();

  // Prepare chart data
  const prepareChartData = () => {
    // Sales trend data (daily/weekly/monthly based on reportType)
    const salesTrendData: { [key: string]: number } = {};
    salesData.forEach(sale => {
      const saleDate = sale.timestamp?.toDate ? sale.timestamp.toDate() : new Date(sale.date);
      let key: string;

      if (reportType === "daily") {
        key = format(saleDate, "MMM dd");
      } else if (reportType === "weekly") {
        key = format(saleDate, "MMM dd");
      } else {
        key = format(saleDate, "MMM yyyy");
      }

      salesTrendData[key] = (salesTrendData[key] || 0) + (sale.amount || 0);
    });

    const salesTrend = Object.entries(salesTrendData).map(([date, amount]) => ({
      date,
      sales: amount
    })).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Category breakdown
    const categoryBreakdown: { [key: string]: number } = {};
    salesData.forEach(sale => {
      if (sale.products) {
        sale.products.forEach(item => {
          const product = inventoryData.find(p => p.id === item.productId);
          const category = product?.category || "Uncategorized";
          categoryBreakdown[category] = (categoryBreakdown[category] || 0) + (item.quantity * item.price);
        });
      }
    });

    const categoryData = Object.entries(categoryBreakdown).map(([name, value]) => ({
      name,
      value: Math.round(value)
    }));

    // Expense breakdown
    const expenseBreakdown: { [key: string]: number } = {};
    expensesData.forEach(expense => {
      expenseBreakdown[expense.category] = (expenseBreakdown[expense.category] || 0) + expense.amount;
    });

    const expenseData = Object.entries(expenseBreakdown).map(([name, value]) => ({
      name,
      value: Math.round(value)
    }));

    return {
      salesTrend,
      categoryData,
      expenseData
    };
  };

  const chartData = prepareChartData();

  // Handle generating report
  const handleGenerateReport = () => {
    toast({
      title: "Report Generated",
      description: `Your ${reportType} ${activeTab} report has been generated successfully.`,
    });
  };

  // Handle download report
  const handleDownloadReport = () => {
    // Create CSV data based on active tab
    let csvData = "";
    let filename = "";

    if (activeTab === "sales") {
      csvData = "Date,Customer,Items,Amount,Payment Method,Status\n";
      salesData.forEach(sale => {
        const date = sale.timestamp?.toDate ? format(sale.timestamp.toDate(), "yyyy-MM-dd") : sale.date;
        csvData += `${date},"${sale.customer}",${sale.items},${sale.amount},"${sale.paymentMethod}","${sale.status}"\n`;
      });
      filename = `sales-report-${format(new Date(), "yyyy-MM-dd")}.csv`;
    } else if (activeTab === "inventory") {
      csvData = "Product,SKU,Category,Stock,Price,Status\n";
      inventoryData.forEach(product => {
        const status = product.stock <= 0 ? "Out of Stock" :
                      product.stock <= (product.threshold || 10) ? "Low Stock" : "In Stock";
        csvData += `"${product.name}","${product.sku || 'N/A'}","${product.category}",${product.stock},${product.price || product.sellingPrice},"${status}"\n`;
      });
      filename = `inventory-report-${format(new Date(), "yyyy-MM-dd")}.csv`;
    } else if (activeTab === "expenses") {
      csvData = "Date,Title,Category,Amount,Payment Method,Status\n";
      expensesData.forEach(expense => {
        csvData += `${expense.date},"${expense.title}","${expense.category}",${expense.amount},"${expense.paymentMethod}","${expense.status}"\n`;
      });
      filename = `expenses-report-${format(new Date(), "yyyy-MM-dd")}.csv`;
    }

    // Create and download file
    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);

    toast({
      title: "Report Downloaded",
      description: `Your ${activeTab} report has been downloaded as ${filename}.`,
    });
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports & Analytics</h1>
          <p className="text-muted-foreground">
            Generate comprehensive reports and analyze your shop's performance.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleGenerateReport}>
            <FileText className="mr-2 h-4 w-4" />
            Generate Report
          </Button>
          <Button onClick={handleDownloadReport}>
            <Download className="mr-2 h-4 w-4" />
            Download CSV
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-5">
          <TabsTrigger value="overview">
            <BarChart3 className="mr-2 h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="sales">
            <ShoppingCart className="mr-2 h-4 w-4" />
            Sales
          </TabsTrigger>
          <TabsTrigger value="inventory">
            <Package className="mr-2 h-4 w-4" />
            Inventory
          </TabsTrigger>
          <TabsTrigger value="expenses">
            <Receipt className="mr-2 h-4 w-4" />
            Expenses
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <TrendingUp className="mr-2 h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key Metrics Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Tsh {metrics.totalSales.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  For selected period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
                <Receipt className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Tsh {metrics.totalExpenses.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  Operating costs
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${metrics.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  Tsh {metrics.profit.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  {metrics.profitMargin.toFixed(1)}% margin
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.lowStockItems}</div>
                <p className="text-xs text-muted-foreground">
                  Need restocking
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Sales Trend</CardTitle>
                <CardDescription>Revenue over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData.salesTrend}>
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`Tsh ${value}`, 'Sales']} />
                      <Line type="monotone" dataKey="sales" stroke="#0088FE" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Sales by Category</CardTitle>
                <CardDescription>Revenue breakdown by product category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RePieChart>
                      <Pie
                        data={chartData.categoryData}
                        cx="50%"
                        cy="50%"
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {chartData.categoryData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`Tsh ${value}`, 'Revenue']} />
                    </RePieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Top Products */}
          <Card>
            <CardHeader>
              <CardTitle>Top Selling Products</CardTitle>
              <CardDescription>Best performing products by revenue</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.topProducts.map((product, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{product.name}</p>
                      <p className="text-sm text-muted-foreground">{product.quantity} units sold</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Tsh {product.revenue.toLocaleString()}</p>
                    </div>
                  </div>
                ))}
                {metrics.topProducts.length === 0 && (
                  <p className="text-center text-muted-foreground py-4">No sales data available</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sales" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sales Report Filters</CardTitle>
              <CardDescription>
                Configure filters for your sales report.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="report-type">Report Period</Label>
                  <Select
                    value={reportType}
                    onValueChange={setReportType}
                  >
                    <SelectTrigger id="report-type">
                      <SelectValue placeholder="Select report period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <div className="grid gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !dateRange && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateRange?.from ? (
                            dateRange.to ? (
                              <>
                                {formatDate(dateRange.from)} - {formatDate(dateRange.to)}
                              </>
                            ) : (
                              formatDate(dateRange.from)
                            )
                          ) : (
                            <span>Pick a date range</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          initialFocus
                          mode="range"
                          defaultMonth={dateRange?.from}
                          selected={dateRange}
                          onSelect={(range) => setDateRange(range || { from: new Date(), to: new Date() })}
                          numberOfMonths={2}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Product Category</Label>
                  <Select
                    value={category}
                    onValueChange={setCategory}
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {isLoadingCategories ? (
                        <SelectItem value="loading" disabled>Loading categories...</SelectItem>
                      ) : (
                        categories.map((category: string) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>

            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Tsh {metrics.totalSales.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  For the selected period
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{salesData.length}</div>
                <p className="text-xs text-muted-foreground">
                  Number of sales
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Sale</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Tsh {metrics.averageSale.toLocaleString(undefined, { maximumFractionDigits: 2 })}</div>
                <p className="text-xs text-muted-foreground">
                  Average transaction value
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Sales Transactions</CardTitle>
              <CardDescription>
                Detailed list of sales transactions for the selected period.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingSales ? (
                <div className="flex justify-center p-4">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : salesData.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  No sales data available for the selected criteria.
                </div>
              ) : (
                <div className="relative w-full overflow-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="h-12 px-4 text-left align-middle font-medium">Date</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Customer</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Items</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Amount</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Payment</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {salesData.map((sale) => (
                        <tr key={sale.id} className="border-b">
                          <td className="p-4 align-middle">
                            {sale.timestamp?.toDate ? format(sale.timestamp.toDate(), "MMM dd, yyyy") :
                             sale.date ? format(new Date(sale.date), "MMM dd, yyyy") : "Unknown date"}
                          </td>
                          <td className="p-4 align-middle">{sale.customer || "Walk-in Customer"}</td>
                          <td className="p-4 align-middle">{sale.items || sale.products?.length || 0}</td>
                          <td className="p-4 align-middle">Tsh {sale.amount?.toLocaleString() || "0"}</td>
                          <td className="p-4 align-middle">{sale.paymentMethod || "Cash"}</td>
                          <td className="p-4 align-middle">
                            <span className={cn(
                              "px-2 py-1 rounded-full text-xs font-medium",
                              sale.status === "completed" ? "bg-green-100 text-green-800" :
                              sale.status === "pending" ? "bg-yellow-100 text-yellow-800" :
                              "bg-red-100 text-red-800"
                            )}>
                              {sale.status || "completed"}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="expenses" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
                <Receipt className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Tsh {metrics.totalExpenses.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  For the selected period
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Number of Expenses</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{expensesData.length}</div>
                <p className="text-xs text-muted-foreground">
                  Total transactions
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Expense</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  Tsh {expensesData.length > 0 ? (metrics.totalExpenses / expensesData.length).toLocaleString(undefined, { maximumFractionDigits: 2 }) : "0"}
                </div>
                <p className="text-xs text-muted-foreground">
                  Average amount
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Expense breakdown chart */}
          <Card>
            <CardHeader>
              <CardTitle>Expenses by Category</CardTitle>
              <CardDescription>Breakdown of expenses by category</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RePieChart>
                    <Pie
                      data={chartData.expenseData}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {chartData.expenseData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`Tsh ${value}`, 'Amount']} />
                  </RePieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Expenses table */}
          <Card>
            <CardHeader>
              <CardTitle>Expense Details</CardTitle>
              <CardDescription>
                Detailed list of expenses for the selected period.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingExpenses ? (
                <div className="flex justify-center p-4">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : expensesData.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  No expense data available for the selected period.
                </div>
              ) : (
                <div className="relative w-full overflow-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="h-12 px-4 text-left align-middle font-medium">Date</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Title</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Category</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Amount</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Payment Method</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {expensesData.map((expense) => (
                        <tr key={expense.id} className="border-b">
                          <td className="p-4 align-middle">
                            {format(new Date(expense.date), "MMM dd, yyyy")}
                          </td>
                          <td className="p-4 align-middle">{expense.title}</td>
                          <td className="p-4 align-middle">{expense.category}</td>
                          <td className="p-4 align-middle">Tsh {expense.amount.toLocaleString()}</td>
                          <td className="p-4 align-middle">{expense.paymentMethod}</td>
                          <td className="p-4 align-middle">
                            <span className={cn(
                              "px-2 py-1 rounded-full text-xs font-medium",
                              expense.status === "approved" ? "bg-green-100 text-green-800" :
                              expense.status === "pending" ? "bg-yellow-100 text-yellow-800" :
                              "bg-red-100 text-red-800"
                            )}>
                              {expense.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{inventoryData.length}</div>
                <p className="text-xs text-muted-foreground">
                  In inventory
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
                <Package className="h-4 w-4 text-amber-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.lowStockItems}</div>
                <p className="text-xs text-muted-foreground">
                  Need restocking
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
                <Package className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {inventoryData.filter(product => product.stock <= 0).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Items unavailable
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  Tsh {inventoryData.reduce((sum, product) =>
                    sum + (product.stock * (product.sellingPrice || product.price || 0)), 0
                  ).toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  Inventory value
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Inventory Details</CardTitle>
              <CardDescription>
                Current inventory levels and status.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingInventory ? (
                <div className="flex justify-center p-4">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : inventoryData.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  No inventory data available.
                </div>
              ) : (
                <div className="relative w-full overflow-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="h-12 px-4 text-left align-middle font-medium">Product</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">SKU</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Category</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Stock</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Price</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Value</th>
                      </tr>
                    </thead>
                    <tbody>
                      {inventoryData.map((product) => (
                        <tr key={product.id} className="border-b">
                          <td className="p-4 align-middle">{product.name}</td>
                          <td className="p-4 align-middle">{product.sku || "N/A"}</td>
                          <td className="p-4 align-middle">{product.category || "Uncategorized"}</td>
                          <td className="p-4 align-middle">
                            <span
                              className={cn(
                                "px-2 py-1 rounded-full text-xs font-medium",
                                product.stock <= 0
                                  ? "bg-red-100 text-red-800"
                                  : product.stock <= (product.threshold || 10)
                                    ? "bg-yellow-100 text-yellow-800"
                                    : "bg-green-100 text-green-800"
                              )}
                            >
                              {product.stock || 0}
                            </span>
                          </td>
                          <td className="p-4 align-middle">
                            Tsh {(product.sellingPrice || product.price || 0).toLocaleString()}
                          </td>
                          <td className="p-4 align-middle">
                            Tsh {(product.stock * (product.sellingPrice || product.price || 0)).toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Sales vs Expenses Trend</CardTitle>
                <CardDescription>Compare revenue and expenses over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData.salesTrend}>
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`Tsh ${value}`, '']} />
                      <Legend />
                      <Line type="monotone" dataKey="sales" stroke="#0088FE" strokeWidth={2} name="Sales" />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Profit Analysis</CardTitle>
                <CardDescription>Profit margin and trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className={`text-3xl font-bold ${metrics.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      Tsh {metrics.profit.toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Net Profit</p>
                  </div>
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${metrics.profitMargin >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {metrics.profitMargin.toFixed(1)}%
                    </div>
                    <p className="text-sm text-muted-foreground">Profit Margin</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-lg font-semibold text-blue-600">
                        Tsh {metrics.totalSales.toLocaleString()}
                      </div>
                      <p className="text-xs text-muted-foreground">Total Revenue</p>
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-red-600">
                        Tsh {metrics.totalExpenses.toLocaleString()}
                      </div>
                      <p className="text-xs text-muted-foreground">Total Expenses</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Key Performance Indicators</CardTitle>
              <CardDescription>Important business metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold">{salesData.length}</div>
                  <p className="text-sm text-muted-foreground">Total Transactions</p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold">
                    Tsh {metrics.averageSale.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                  </div>
                  <p className="text-sm text-muted-foreground">Average Sale Value</p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold">{categories.length}</div>
                  <p className="text-sm text-muted-foreground">Product Categories</p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold">{metrics.lowStockItems}</div>
                  <p className="text-sm text-muted-foreground">Items Need Restock</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
